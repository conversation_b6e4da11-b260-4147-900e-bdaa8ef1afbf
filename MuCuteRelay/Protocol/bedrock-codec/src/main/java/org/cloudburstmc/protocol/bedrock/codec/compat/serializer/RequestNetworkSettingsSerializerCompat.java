package org.cloudburstmc.protocol.bedrock.codec.compat.serializer;

import io.netty.buffer.ByteBuf;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.cloudburstmc.protocol.bedrock.codec.BedrockCodecHelper;
import org.cloudburstmc.protocol.bedrock.codec.BedrockPacketSerializer;
import org.cloudburstmc.protocol.bedrock.packet.RequestNetworkSettingsPacket;

@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class RequestNetworkSettingsSerializerCompat implements BedrockPacketSerializer<RequestNetworkSettingsPacket> {

    public static final RequestNetworkSettingsSerializerCompat INSTANCE = new RequestNetworkSettingsSerializerCompat();

    @Override
    public void serialize(ByteBuf buffer, BedrockCodecHelper helper, RequestNetworkSettingsPacket packet) {
        buffer.writeInt(packet.getProtocolVersion());
    }

    @Override
    public void deserialize(ByteBuf buffer, BedrockCodec<PERSON><PERSON><PERSON> helper, RequestNetworkSettingsPacket packet) {
        packet.setProtocolVersion(buffer.readInt());
    }
}