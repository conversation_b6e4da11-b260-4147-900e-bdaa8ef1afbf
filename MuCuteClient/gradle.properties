org.gradle.jvmargs=-Xmx4096M -Dkotlin.daemon.jvm.options="-Xmx4096M" -Dfile.encoding=UTF-8 -XX:+UseParallelGC
android.useAndroidX=true
android.enableR8.fullMode=true
android.enableAppCompileTimeRClass=true
android.nonTransitiveRClass=true
org.gradle.caching=true
org.gradle.parallel=true
org.gradle.unsafe.configuration-cache=true
kotlin.code.style=official
android.defaults.buildfeatures.buildconfig=false
android.defaults.buildfeatures.aidl=false
android.defaults.buildfeatures.renderscript=false
android.defaults.buildfeatures.resvalues=false
android.defaults.buildfeatures.shaders=false
android.injected.testOnly=false
android.nonFinalResIds=false